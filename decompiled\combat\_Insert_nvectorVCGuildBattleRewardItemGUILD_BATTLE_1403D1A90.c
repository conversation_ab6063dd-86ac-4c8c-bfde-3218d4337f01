/*
 * Function: ?_Insert_n@?$vector@VCGuildBattleRewardItem@GUILD_BATTLE@@V?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@@std@@IEAAXV?$_Vector_iterator@VCGuildBattleRewardItem@GUILD_BATTLE@@V?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@@2@_KAEBVCGuildBattleRewardItem@GUILD_BATTLE@@@Z
 * Address: 0x1403D1A90
 */

void __fastcall std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::_Insert_n(std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > *this, std::_Vector_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > *_Where, unsigned __int64 _Count, GUILD_BATTLE::CGuildBattleRewardItem *_Val)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int64 v6; // rax@5
  unsigned __int64 v7; // rax@7
  unsigned __int64 v8; // rax@8
  unsigned __int64 v9; // rax@11
  __int64 v10; // [sp+0h] [bp-C8h]@1
  GUILD_BATTLE::CGuildBattleRewardItem _Vala; // [sp+28h] [bp-A0h]@4
  unsigned __int64 _Counta; // [sp+48h] [bp-80h]@4
  GUILD_BATTLE::CGuildBattleRewardItem *_Ptr; // [sp+50h] [bp-78h]@13
  GUILD_BATTLE::CGuildBattleRewardItem *v14; // [sp+58h] [bp-70h]@13
  GUILD_BATTLE::CGuildBattleRewardItem *_Last; // [sp+60h] [bp-68h]@18
  char v16; // [sp+68h] [bp-60h]@4
  __int64 v17; // [sp+78h] [bp-50h]@4
  unsigned __int64 v18; // [sp+80h] [bp-48h]@5
  unsigned __int64 v19; // [sp+88h] [bp-40h]@8
  unsigned __int64 v20; // [sp+90h] [bp-38h]@9
  GUILD_BATTLE::CGuildBattleRewardItem *v21; // [sp+98h] [bp-30h]@13
  GUILD_BATTLE::CGuildBattleRewardItem *v22; // [sp+A0h] [bp-28h]@13
  std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > *v23; // [sp+D0h] [bp+8h]@1
  std::_Vector_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > *v24; // [sp+D8h] [bp+10h]@1
  unsigned __int64 v25; // [sp+E0h] [bp+18h]@1
  unsigned __int64 v26; // [sp+E0h] [bp+18h]@13

  v25 = _Count;
  v24 = _Where;
  v23 = this;
  v4 = &v10;
  for ( i = 46i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v17 = -2i64;
  qmemcpy(&v16, _Val, 0x10ui64);
  qmemcpy(&_Vala, &v16, sizeof(_Vala));
  _Counta = std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::capacity(v23);
  if ( v25 )
  {
    v18 = std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::size(v23);
    v6 = std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::max_size(v23);
    if ( v6 - v18 < v25 )
      std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::_Xlen();
    v7 = std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::size(v23);
    if ( _Counta >= v25 + v7 )
    {
      if ( v23->_Mylast - v24->_Myptr >= v25 )
      {
        _Last = v23->_Mylast;
        v23->_Mylast = std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::_Umove<GUILD_BATTLE::CGuildBattleRewardItem *>(
                         v23,
                         &_Last[-v25],
                         _Last,
                         v23->_Mylast);
        stdext::_Unchecked_move_backward<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem *>(
          v24->_Myptr,
          &_Last[-v25],
          _Last);
        std::fill<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem>(
          v24->_Myptr,
          &v24->_Myptr[v25],
          &_Vala);
      }
      else
      {
        std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::_Umove<GUILD_BATTLE::CGuildBattleRewardItem *>(
          v23,
          v24->_Myptr,
          v23->_Mylast,
          &v24->_Myptr[v25]);
        std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::_Ufill(
          v23,
          v23->_Mylast,
          v25 - (v23->_Mylast - v24->_Myptr),
          &_Vala);
        v23->_Mylast += v25;
        std::fill<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem>(
          v24->_Myptr,
          &v23->_Mylast[-v25],
          &_Vala);
      }
    }
    else
    {
      v19 = _Counta / 2;
      v8 = std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::max_size(v23);
      if ( v8 - v19 >= _Counta )
        v20 = _Counta / 2 + _Counta;
      else
        v20 = 0i64;
      _Counta = v20;
      v9 = std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::size(v23);
      if ( _Counta < v25 + v9 )
        _Counta = v25
                + std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::size(v23);
      _Ptr = std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>::allocate(&v23->_Alval, _Counta);
      v14 = _Ptr;
      v21 = std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::_Umove<GUILD_BATTLE::CGuildBattleRewardItem *>(
              v23,
              v23->_Myfirst,
              v24->_Myptr,
              _Ptr);
      v14 = v21;
      v22 = std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::_Ufill(
              v23,
              v21,
              v25,
              &_Vala);
      v14 = v22;
      std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::_Umove<GUILD_BATTLE::CGuildBattleRewardItem *>(
        v23,
        v24->_Myptr,
        v23->_Mylast,
        v22);
      v26 = std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::size(v23)
          + v25;
      if ( v23->_Myfirst )
      {
        std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::_Destroy(
          v23,
          v23->_Myfirst,
          v23->_Mylast);
        std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>::deallocate(
          &v23->_Alval,
          v23->_Myfirst,
          v23->_Myend - v23->_Myfirst);
      }
      v23->_Myend = &_Ptr[_Counta];
      v23->_Mylast = &_Ptr[v26];
      v23->_Myfirst = _Ptr;
    }
  }
  std::_Vector_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::~_Vector_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>(v24);
}
