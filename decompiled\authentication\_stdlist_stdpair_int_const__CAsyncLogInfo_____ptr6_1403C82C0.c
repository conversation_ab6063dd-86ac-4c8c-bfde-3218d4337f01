/*
 * Function: _std::list_std::pair_int_const__CAsyncLogInfo_____ptr64__std::allocator_std::pair_int_const__CAsyncLogInfo_____ptr64_____::_Insert_std::list_std::pair_int_const__CAsyncLogInfo_____ptr64__std::allocator_std::pair_int_const__CAsyncLogInfo_____ptr64_____::_Iterator_0____::_1_::catch$0
 * Address: 0x1403C82C0
 */

void __fastcall __noreturn std::list_std::pair_int_const__CAsyncLogInfo_____ptr64__std::allocator_std::pair_int_const__CAsyncLogInfo_____ptr64_____::_Insert_std::list_std::pair_int_const__CAsyncLogInfo_____ptr64__std::allocator_std::pair_int_const__CAsyncLogInfo_____ptr64_____::_Iterator_0____::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 i; // rbp@1
  std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *v3; // rax@3
  __int64 v4; // rax@3

  for ( i = a2;
        std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Const_iterator<0>::operator!=(
          (std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Const_iterator<0> *)(i + 40),
          *(std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Const_iterator<0> **)(i + 288));
        std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>::operator++((std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *)(i + 40)) )
  {
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>::_Iterator<0>(
      (std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *)(i + 88),
      *(std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> **)(i + 280));
    *(_QWORD *)(i + 176) = i + 152;
    v3 = std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>::operator--((std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *)(i + 88));
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>::_Iterator<0>(
      *(std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> **)(i + 176),
      v3);
    *(_QWORD *)(i + 240) = v4;
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::erase(
      *(std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > > **)(i + 272),
      (std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *)(i + 184),
      *(std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> **)(i + 240));
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>::~_Iterator<0>((std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *)(i + 184));
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>::~_Iterator<0>((std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *)(i + 88));
  }
  CxxThrowException_0(0i64, 0i64);
}
