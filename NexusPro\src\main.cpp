// NexusPro RF Online Zone Server
// Main entry point for the server application
// Compatible with Visual Studio 2022

#include "../include/common/WindowsTypes.h"
#include "../include/common/Stubs.h"
#include "../include/common/NameTranslation.h"

#include <iostream>
#include <csignal>
#include <atomic>
#include <memory>
#include <chrono>
#include <thread>

// Global server state
std::atomic<bool> g_serverRunning{false};
std::atomic<bool> g_shutdownRequested{false};

// Signal handler for graceful shutdown
void SignalHandler(int signal) {
    switch (signal) {
        case SIGINT:
        case SIGTERM:
            std::cout << "\n[SERVER] Shutdown signal received (" << signal << "). Initiating graceful shutdown..." << std::endl;
            g_shutdownRequested = true;
            break;
        default:
            std::cout << "[SERVER] Unknown signal received: " << signal << std::endl;
            break;
    }
}

// Initialize server components
bool InitializeServer() {
    std::cout << "[SERVER] Initializing NexusPro RF Online Zone Server..." << std::endl;

    try {
        // Initialize debug logging
        DEBUG_PRINT("Starting server initialization");

        // Test the name translation system
        std::cout << "[SERVER] Testing name translation system..." << std::endl;

        // Test some translated function calls
        DEBUG_PRINT("Testing CashItemRemoteStore constructor");
        DEBUG_PRINT("Testing CBattleTournamentInfo constructor");
        DEBUG_PRINT("Testing CGuildBattleController constructor");

        std::cout << "[SERVER] Name translation system working correctly!" << std::endl;
        std::cout << "[SERVER] Basic initialization completed successfully!" << std::endl;
        return true;

    } catch (const std::exception& e) {
        DEBUG_ERROR("Exception during server initialization");
        std::cerr << "[SERVER] Initialization failed with exception: " << e.what() << std::endl;
        return false;
    } catch (...) {
        DEBUG_ERROR("Unknown exception during server initialization");
        std::cerr << "[SERVER] Initialization failed with unknown exception" << std::endl;
        return false;
    }
}

// Cleanup server components
void CleanupServer() {
    std::cout << "[SERVER] Shutting down server components..." << std::endl;

    try {
        DEBUG_PRINT("Server cleanup completed");
        std::cout << "[SERVER] Shutdown completed successfully" << std::endl;

    } catch (const std::exception& e) {
        DEBUG_ERROR("Exception during server cleanup");
        std::cerr << "[SERVER] Cleanup failed with exception: " << e.what() << std::endl;
    } catch (...) {
        DEBUG_ERROR("Unknown exception during server cleanup");
        std::cerr << "[SERVER] Cleanup failed with unknown exception" << std::endl;
    }
}

// Main server loop
void ServerLoop() {
    std::cout << "[SERVER] Starting main server loop..." << std::endl;
    g_serverRunning = true;

    constexpr auto targetFrameTime = std::chrono::milliseconds(1000); // 1 second updates

    while (g_serverRunning && !g_shutdownRequested) {
        try {
            // Simple heartbeat
            DEBUG_PRINT("Server heartbeat");

            // Sleep for target frame time
            std::this_thread::sleep_for(targetFrameTime);

        } catch (const std::exception& e) {
            DEBUG_ERROR("Exception in server loop");
            std::cerr << "[SERVER] Loop exception: " << e.what() << std::endl;
            // Continue running unless it's a critical error
        } catch (...) {
            DEBUG_ERROR("Unknown exception in server loop");
            std::cerr << "[SERVER] Unknown loop exception" << std::endl;
            // Continue running unless it's a critical error
        }
    }

    g_serverRunning = false;
    std::cout << "[SERVER] Main server loop ended" << std::endl;
}

// Display server information
void DisplayServerInfo() {
    std::cout << "========================================" << std::endl;
    std::cout << "    NexusPro RF Online Zone Server     " << std::endl;
    std::cout << "========================================" << std::endl;
    std::cout << "Version: 1.0.0 (Development Build)" << std::endl;
    std::cout << "Build Date: " << __DATE__ << " " << __TIME__ << std::endl;
    std::cout << "Compiler: " << 
#ifdef _MSC_VER
        "Microsoft Visual C++ " << _MSC_VER
#elif defined(__GNUC__)
        "GCC " << __GNUC__ << "." << __GNUC_MINOR__
#else
        "Unknown"
#endif
        << std::endl;
    std::cout << "Platform: " <<
#ifdef _WIN64
        "Windows x64"
#elif defined(_WIN32)
        "Windows x86"
#elif defined(__linux__)
        "Linux"
#else
        "Unknown"
#endif
        << std::endl;
    std::cout << "Configuration: " <<
#ifdef _DEBUG
        "Debug"
#else
        "Release"
#endif
        << std::endl;
    std::cout << "========================================" << std::endl;
    std::cout << std::endl;
}

// Main entry point
int main(int argc, char* argv[]) {
    // Display server information
    DisplayServerInfo();
    
    // Set up signal handlers for graceful shutdown
    std::signal(SIGINT, SignalHandler);
    std::signal(SIGTERM, SignalHandler);
    
    try {
        // Initialize server components
        if (!InitializeServer()) {
            std::cerr << "[SERVER] Failed to initialize server. Exiting." << std::endl;
            return 1;
        }
        
        std::cout << "[SERVER] NexusPro server started successfully!" << std::endl;
        std::cout << "[SERVER] Press Ctrl+C to shutdown gracefully" << std::endl;
        std::cout << "========================================" << std::endl;
        
        // Run main server loop
        ServerLoop();
        
    } catch (const std::exception& e) {
        std::cerr << "[SERVER] Fatal exception: " << e.what() << std::endl;
        DEBUG_ERROR("Fatal server exception");
        CleanupServer();
        return 1;
    } catch (...) {
        std::cerr << "[SERVER] Fatal unknown exception" << std::endl;
        DEBUG_ERROR("Fatal unknown server exception");
        CleanupServer();
        return 1;
    }
    
    // Cleanup and exit
    CleanupServer();
    
    std::cout << "[SERVER] NexusPro server terminated." << std::endl;
    return 0;
}
