/*
 * Function: ?_buybygold_buy_single_item_setbuydblog@CashItemRemoteStore@@AEAAXPEAU_param_cashitem_dblog@@AEAU_db_con@_STORAGE_LIST@@KK@Z
 * Address: 0x140300760
 */

void __fastcall CashItemRemoteStore::_buybygold_buy_single_item_setbuydblog(CashItemRemoteStore *this, _param_cashitem_dblog *pSheet, _STORAGE_LIST::_db_con *GiveItem, unsigned int dwPrice, unsigned int dwDiscountRate)
{
  pSheet->data[pSheet->nBuyNum].byRet = 0;
  pSheet->data[pSheet->nBuyNum].byTblCode = GiveItem->m_byTableCode;
  pSheet->data[pSheet->nBuyNum].wItemIndex = GiveItem->m_wItemIndex;
  pSheet->data[pSheet->nBuyNum].byOverlapNum = GiveItem->m_dwDur;
  pSheet->data[pSheet->nBuyNum].dwCost = dwPrice;
  pSheet->data[pSheet->nBuyNum++].iCashDiscount = dwDiscountRate;
}
