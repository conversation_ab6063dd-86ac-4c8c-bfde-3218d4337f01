/*
 * Function: ?UpdateWinLose@CGuildBattleController@@QEAA_NEKEK@Z
 * Address: 0x1403D6C10
 */

bool __fastcall CGuildBattleController::UpdateWinLose(CGuildBattleController *this, char byWinR<PERSON>, unsigned int dwWinGuildSerial, char byLoseRace, unsigned int dwLoseGuildSerial)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleRankManager *v7; // rax@4
  __int64 v9; // [sp+0h] [bp-38h]@1
  char v10; // [sp+48h] [bp+10h]@1
  unsigned int dwWinGuildSeriala; // [sp+50h] [bp+18h]@1
  char v12; // [sp+58h] [bp+20h]@1

  v12 = byLoseRace;
  dwWinGuildSeriala = dwWinGuildSerial;
  v10 = byWinRace;
  v5 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v7 = GUILD_BATTLE::CGuildBattleRankManager::Instance();
  return GUILD_BATTLE::CGuildBattleRankManager::UpdateWinLose(v7, v10, dwWinGuildSeriala, v12, dwLoseGuildSerial) != 0;
}
