/*
 * Function: ?_ReadGoods@CashItemRemoteStore@@AEAA_NXZ
 * Address: 0x1402F4A90
 */

char __fastcall CashItemRemoteStore::_ReadGoods(CashItemRemoteStore *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-1B8h]@1
  char pszErrMsg; // [sp+30h] [bp-188h]@4
  CRecordData krecPrice; // [sp+D0h] [bp-E8h]@8
  char v7; // [sp+190h] [bp-28h]@9
  char v8; // [sp+191h] [bp-27h]@11
  char v9; // [sp+192h] [bp-26h]@12
  __int64 v10; // [sp+198h] [bp-20h]@4
  unsigned __int64 v11; // [sp+1A0h] [bp-18h]@4
  CashItemRemoteStore *v12; // [sp+1C0h] [bp+8h]@1

  v12 = this;
  v1 = &v4;
  for ( i = 108i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v10 = -2i64;
  v11 = (unsigned __int64)&v4 ^ _security_cookie;
  if ( CRecordData::ReadRecord(&v12->_kRecGoods, ".\\Script\\CashShop.dat", 0xF0u, &pszErrMsg) )
  {
    if ( CashItemRemoteStore::_MakeLinkTable(v12, &pszErrMsg, 128) )
    {
      CRecordData::CRecordData(&krecPrice);
      if ( CashItemRemoteStore::LoadNationalPrice(v12, &krecPrice) )
      {
        if ( CashItemRemoteStore::__CheckGoods(v12, &krecPrice) )
        {
          v9 = 1;
          CRecordData::~CRecordData(&krecPrice);
          result = v9;
        }
        else
        {
          v8 = 0;
          CRecordData::~CRecordData(&krecPrice);
          result = v8;
        }
      }
      else
      {
        MyMessageBox("CashItemRemoteStore::_ReadGoods()", "LoadNationalPrice() Failed!");
        CLogFile::Write(&stru_1799C8F30, "CashItemRemoteStore::_ReadGoods() LoadNationalPrice() Failed!");
        v7 = 0;
        CRecordData::~CRecordData(&krecPrice);
        result = v7;
      }
    }
    else
    {
      MyMessageBox("CashItemRemoteStore", &pszErrMsg);
      CLogFile::Write(&stru_1799C8F30, "Failed CashItemRemoteStore::_MakeLinkTable()");
      result = 0;
    }
  }
  else
  {
    MyMessageBox("CashItemRemoteStore", &pszErrMsg);
    result = 0;
  }
  return result;
}
