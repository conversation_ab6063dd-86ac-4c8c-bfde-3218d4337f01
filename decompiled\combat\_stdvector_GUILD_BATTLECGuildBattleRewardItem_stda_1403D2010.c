/*
 * Function: _std::vector_GUILD_BATTLE::CGuildBattleRewardItem_std::allocator_GUILD_BATTLE::CGuildBattleRewardItem___::_Insert_n_::_1_::catch$0
 * Address: 0x1403D2010
 */

void __fastcall __noreturn std::vector_GUILD_BATTLE::CGuildBattleRewardItem_std::allocator_GUILD_BATTLE::CGuildBattleRewardItem___::_Insert_n_::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 v2; // rbp@1

  v2 = a2;
  std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::_Destroy(
    *(std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > **)(a2 + 208),
    *(GUILD_BATTLE::CGuildBattleRewardItem **)(a2 + 80),
    *(GUILD_BATTLE::CGuildBattleRewardItem **)(a2 + 88));
  std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>::deallocate(
    (std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> *)(*(_QWORD *)(v2 + 208) + 8i64),
    *(GUILD_BATTLE::CGuildBattleRewardItem **)(v2 + 80),
    *(_QWORD *)(v2 + 72));
  CxxThrowException_0(0i64, 0i64);
}
