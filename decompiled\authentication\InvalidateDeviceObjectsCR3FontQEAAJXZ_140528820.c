/*
 * Function: ?InvalidateDeviceObjects@CR3Font@@QEAAJXZ
 * Address: 0x140528820
 */

__int64 __fastcall CR3Font::InvalidateDeviceObjects(CR3Font *this)
{
  CR3Font *v1; // rbx@1
  __int64 v2; // rcx@1

  v1 = this;
  v2 = *(_QWORD *)this;
  if ( v2 )
  {
    if ( *((_DWORD *)v1 + 25) )
      (*(void (**)(void))(*(_QWORD *)v2 + 448i64))();
    if ( *((_DWORD *)v1 + 26) )
      (*(void (**)(void))(**(_QWORD **)v1 + 448i64))();
  }
  *((_DWORD *)v1 + 25) = 0;
  *((_DWORD *)v1 + 26) = 0;
  CR3Font::PrivateRelease(v1);
  return 0i64;
}
