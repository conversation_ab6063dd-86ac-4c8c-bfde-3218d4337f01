#!/usr/bin/env powershell

# Script to verify the organization of files in the NexusPro project
Write-Host "Verifying NexusPro project file organization..." -ForegroundColor Green

$projectRoot = "NexusPro"
$vcxprojPath = "$projectRoot\NexusPro.vcxproj"
$filtersPath = "$projectRoot\NexusPro.vcxproj.filters"

# Check if files exist
if (-not (Test-Path $vcxprojPath)) {
    Write-Error "Project file not found: $vcxprojPath"
    exit 1
}

if (-not (Test-Path $filtersPath)) {
    Write-Error "Filters file not found: $filtersPath"
    exit 1
}

Write-Host "✅ Project files found" -ForegroundColor Green

# Define expected systems
$expectedSystems = @(
    "authentication", "combat", "common", "database", "economy", 
    "items", "network", "player", "security", "system", "world"
)

# Check project file content
Write-Host "`nChecking main project file..." -ForegroundColor Yellow
$projectContent = Get-Content $vcxprojPath -Raw

$sourceFileCount = ([regex]::Matches($projectContent, '<ClCompile Include=')).Count
$headerFileCount = ([regex]::Matches($projectContent, '<ClInclude Include=')).Count

Write-Host "  Source files in project: $sourceFileCount" -ForegroundColor Cyan
Write-Host "  Header files in project: $headerFileCount" -ForegroundColor Cyan

# Check filters file content
Write-Host "`nChecking filters file..." -ForegroundColor Yellow
$filtersContent = Get-Content $filtersPath -Raw

# Check filter definitions
$missingFilters = @()
foreach ($system in $expectedSystems) {
    $systemName = (Get-Culture).TextInfo.ToTitleCase($system.ToLower())
    $sourceFilter = "Source Files\$systemName"
    $headerFilter = "Header Files\$systemName"
    
    if ($filtersContent -notmatch [regex]::Escape("Filter Include=`"$sourceFilter`"")) {
        $missingFilters += $sourceFilter
    }
    if ($filtersContent -notmatch [regex]::Escape("Filter Include=`"$headerFilter`"")) {
        $missingFilters += $headerFilter
    }
}

if ($missingFilters.Count -eq 0) {
    Write-Host "  ✅ All filter definitions found" -ForegroundColor Green
} else {
    Write-Host "  ❌ Missing filters:" -ForegroundColor Red
    $missingFilters | ForEach-Object { Write-Host "    - $_" -ForegroundColor Red }
}

# Check specific file organization
Write-Host "`nChecking specific file organization..." -ForegroundColor Yellow

# Check __CheckGoods_.cpp specifically
$checkGoodsPattern = '__CheckGoods_\.cpp'
if ($filtersContent -match $checkGoodsPattern) {
    if ($filtersContent -match '__CheckGoods_\.cpp.*?<Filter>Source Files\\Combat</Filter>') {
        Write-Host "  ✅ __CheckGoods_.cpp is properly assigned to Source Files\Combat" -ForegroundColor Green
    } else {
        Write-Host "  ❌ __CheckGoods_.cpp is not properly assigned to Combat folder" -ForegroundColor Red
    }
} else {
    Write-Host "  ❌ __CheckGoods_.cpp not found in filters file" -ForegroundColor Red
}

# Count files per system in filters
Write-Host "`nFile distribution by system:" -ForegroundColor Yellow
foreach ($system in $expectedSystems) {
    $systemName = (Get-Culture).TextInfo.ToTitleCase($system.ToLower())
    
    $sourcePattern = "Source Files\\$systemName"
    $headerPattern = "Header Files\\$systemName"
    
    $sourceMatches = ([regex]::Matches($filtersContent, [regex]::Escape("<Filter>$sourcePattern</Filter>"))).Count
    $headerMatches = ([regex]::Matches($filtersContent, [regex]::Escape("<Filter>$headerPattern</Filter>"))).Count
    
    Write-Host "  $systemName`: $sourceMatches source + $headerMatches header files" -ForegroundColor Cyan
}

# Check for unassigned files
Write-Host "`nChecking for unassigned files..." -ForegroundColor Yellow
$totalFiltersAssignments = ([regex]::Matches($filtersContent, '<Filter>Source Files\\')).Count + ([regex]::Matches($filtersContent, '<Filter>Header Files\\')).Count
$totalProjectFiles = $sourceFileCount + $headerFileCount

if ($totalFiltersAssignments -eq $totalProjectFiles) {
    Write-Host "  ✅ All files are assigned to filters ($totalFiltersAssignments assignments)" -ForegroundColor Green
} else {
    Write-Host "  ⚠️  Mismatch: $totalProjectFiles files in project, $totalFiltersAssignments filter assignments" -ForegroundColor Yellow
}

Write-Host "`n" + "="*60 -ForegroundColor White
Write-Host "VERIFICATION SUMMARY" -ForegroundColor White
Write-Host "="*60 -ForegroundColor White

Write-Host "Project File Status:" -ForegroundColor Yellow
Write-Host "  • Source files: $sourceFileCount" -ForegroundColor Cyan
Write-Host "  • Header files: $headerFileCount" -ForegroundColor Cyan
Write-Host "  • Total files: $($sourceFileCount + $headerFileCount)" -ForegroundColor Cyan

Write-Host "`nFilters File Status:" -ForegroundColor Yellow
Write-Host "  • Filter assignments: $totalFiltersAssignments" -ForegroundColor Cyan
Write-Host "  • Systems covered: $($expectedSystems.Count)" -ForegroundColor Cyan

Write-Host "`nNext Steps for Visual Studio:" -ForegroundColor Yellow
Write-Host "  1. Close Visual Studio completely" -ForegroundColor White
Write-Host "  2. Reopen NexusPro.sln" -ForegroundColor White
Write-Host "  3. Check Solution Explorer for organized folders" -ForegroundColor White
Write-Host "  4. Verify __CheckGoods_.cpp appears under Source Files\Combat" -ForegroundColor White

if ($missingFilters.Count -eq 0 -and $totalFiltersAssignments -eq $totalProjectFiles) {
    Write-Host "`n✅ PROJECT ORGANIZATION IS COMPLETE AND READY!" -ForegroundColor Green
} else {
    Write-Host "`n⚠️  SOME ISSUES DETECTED - SEE DETAILS ABOVE" -ForegroundColor Yellow
}

Write-Host "`nIf Visual Studio doesn't show the organization:" -ForegroundColor Yellow
Write-Host "  • Try 'Build > Clean Solution' then 'Build > Rebuild Solution'" -ForegroundColor White
Write-Host "  • Check if .vcxproj.filters file is read-only" -ForegroundColor White
Write-Host "  • Verify Visual Studio has reloaded the project files" -ForegroundColor White
