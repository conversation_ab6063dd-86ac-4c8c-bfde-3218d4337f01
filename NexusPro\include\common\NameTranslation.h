#pragma once

// Name Translation System for RF Online Decompiled Code
// This file provides a mapping between mangled C++ names and proper function declarations
// while preserving the original decompiled source code structure

#include "WindowsTypes.h"
#include "Stubs.h"

// Forward declarations for RF Online classes
class CashItemRemoteStore;
class CBattleTournamentInfo;
class CGuildBattleController;
class CRFCashItemDatabase;
class MonsterSFContDamageToleracne;

// Forward declarations for RF Online structures
struct _attack_count_result_zocl;
struct _ATTACK_DELAY_CHECKER;
struct _attack_force_result_zocl;
struct _attack_gen_result_zocl;
struct _attack_keeper_inform_zocl;
struct _attack_param;
struct _attack_selfdestruction_result_zocl;
struct _attack_siege_result_zocl;
struct _attack_trap_inform_zocl;
struct _attack_unit_result_zocl;
struct _be_damaged_char;
struct _guild_battle_suggest_matter;
struct _param_cashitem_dblog;
struct _personal_automine_attacked_zocl;
struct _possible_battle_guild_list_result_zocl;

namespace NameTranslation {

    // Mangled Name Translation Patterns
    // Microsoft Visual C++ name mangling follows specific patterns:
    // ??0 = Constructor
    // ??1 = Destructor
    // @@QEAA = public member function, 64-bit
    // @@IEAA = protected member function, 64-bit
    // @XZ = void parameter list terminator

    // Combat System Function Mappings
    // Original: ??0CashItemRemoteStore@@QEAA@XZ
    // Translated: CashItemRemoteStore_Constructor
    inline void CashItemRemoteStore_Constructor(CashItemRemoteStore* pThis) {
        DEBUG_PRINT("CashItemRemoteStore constructor called");
        UNUSED_PARAM(pThis);
        // Implementation will be provided by corresponding .cpp file
    }

    // Original: ??0CBattleTournamentInfo@@QEAA@XZ
    // Translated: CBattleTournamentInfo_Constructor
    inline void CBattleTournamentInfo_Constructor(CBattleTournamentInfo* pThis) {
        DEBUG_PRINT("CBattleTournamentInfo constructor called");
        UNUSED_PARAM(pThis);
    }

    // Original: ??0CGuildBattleController@@IEAA@XZ
    // Translated: CGuildBattleController_Constructor
    inline void CGuildBattleController_Constructor(CGuildBattleController* pThis) {
        DEBUG_PRINT("CGuildBattleController constructor called");
        UNUSED_PARAM(pThis);
    }

    // Original: ??0CRFCashItemDatabase@@QEAA@XZ
    // Translated: CRFCashItemDatabase_Constructor
    inline void CRFCashItemDatabase_Constructor(CRFCashItemDatabase* pThis) {
        DEBUG_PRINT("CRFCashItemDatabase constructor called");
        UNUSED_PARAM(pThis);
    }

    // Original: ??0MonsterSFContDamageToleracne@@QEAA@XZ
    // Translated: MonsterSFContDamageToleracne_Constructor
    inline void MonsterSFContDamageToleracne_Constructor(MonsterSFContDamageToleracne* pThis) {
        DEBUG_PRINT("MonsterSFContDamageToleracne constructor called");
        UNUSED_PARAM(pThis);
    }

    // Structure Constructors
    // Original: ??0_attack_count_result_zocl@@QEAA@XZ
    inline void attack_count_result_zocl_Constructor(_attack_count_result_zocl* pThis) {
        DEBUG_PRINT("_attack_count_result_zocl constructor called");
        UNUSED_PARAM(pThis);
    }

    // Original: ??0_ATTACK_DELAY_CHECKER@@QEAA@XZ
    inline void ATTACK_DELAY_CHECKER_Constructor(_ATTACK_DELAY_CHECKER* pThis) {
        DEBUG_PRINT("_ATTACK_DELAY_CHECKER constructor called");
        UNUSED_PARAM(pThis);
    }

    // Original: ??0_attack_force_result_zocl@@QEAA@XZ
    inline void attack_force_result_zocl_Constructor(_attack_force_result_zocl* pThis) {
        DEBUG_PRINT("_attack_force_result_zocl constructor called");
        UNUSED_PARAM(pThis);
    }

    // Original: ??0_attack_gen_result_zocl@@QEAA@XZ
    inline void attack_gen_result_zocl_Constructor(_attack_gen_result_zocl* pThis) {
        DEBUG_PRINT("_attack_gen_result_zocl constructor called");
        UNUSED_PARAM(pThis);
    }

    // Original: ??0_attack_keeper_inform_zocl@@QEAA@XZ
    inline void attack_keeper_inform_zocl_Constructor(_attack_keeper_inform_zocl* pThis) {
        DEBUG_PRINT("_attack_keeper_inform_zocl constructor called");
        UNUSED_PARAM(pThis);
    }

    // Original: ??0_attack_param@@QEAA@XZ
    inline void attack_param_Constructor(_attack_param* pThis) {
        DEBUG_PRINT("_attack_param constructor called");
        UNUSED_PARAM(pThis);
    }

    // Original: ??0_attack_selfdestruction_result_zocl@@QEAA@XZ
    inline void attack_selfdestruction_result_zocl_Constructor(_attack_selfdestruction_result_zocl* pThis) {
        DEBUG_PRINT("_attack_selfdestruction_result_zocl constructor called");
        UNUSED_PARAM(pThis);
    }

    // Original: ??0_attack_siege_result_zocl@@QEAA@XZ
    inline void attack_siege_result_zocl_Constructor(_attack_siege_result_zocl* pThis) {
        DEBUG_PRINT("_attack_siege_result_zocl constructor called");
        UNUSED_PARAM(pThis);
    }

    // Original: ??0_attack_trap_inform_zocl@@QEAA@XZ
    inline void attack_trap_inform_zocl_Constructor(_attack_trap_inform_zocl* pThis) {
        DEBUG_PRINT("_attack_trap_inform_zocl constructor called");
        UNUSED_PARAM(pThis);
    }

    // Original: ??0_attack_unit_result_zocl@@QEAA@XZ
    inline void attack_unit_result_zocl_Constructor(_attack_unit_result_zocl* pThis) {
        DEBUG_PRINT("_attack_unit_result_zocl constructor called");
        UNUSED_PARAM(pThis);
    }

    // Original: ??0_be_damaged_char@@QEAA@XZ
    inline void be_damaged_char_Constructor(_be_damaged_char* pThis) {
        DEBUG_PRINT("_be_damaged_char constructor called");
        UNUSED_PARAM(pThis);
    }

    // Original: ??0_guild_battle_suggest_matter@@QEAA@XZ
    inline void guild_battle_suggest_matter_Constructor(_guild_battle_suggest_matter* pThis) {
        DEBUG_PRINT("_guild_battle_suggest_matter constructor called");
        UNUSED_PARAM(pThis);
    }

    // Original: ??0_param_cashitem_dblog@@QEAA@K@Z
    inline void param_cashitem_dblog_Constructor(_param_cashitem_dblog* pThis, unsigned int dwAv) {
        DEBUG_PRINTF("_param_cashitem_dblog constructor called with dwAv=%u", dwAv);
        UNUSED_PARAM(pThis);
    }

    // Original: ??0_personal_automine_attacked_zocl@@QEAA@XZ
    inline void personal_automine_attacked_zocl_Constructor(_personal_automine_attacked_zocl* pThis) {
        DEBUG_PRINT("_personal_automine_attacked_zocl constructor called");
        UNUSED_PARAM(pThis);
    }

    // Original: ??0_possible_battle_guild_list_result_zocl@@QEAA@XZ
    inline void possible_battle_guild_list_result_zocl_Constructor(_possible_battle_guild_list_result_zocl* pThis) {
        DEBUG_PRINT("_possible_battle_guild_list_result_zocl constructor called");
        UNUSED_PARAM(pThis);
    }

    // Utility Functions for Name Translation
    namespace Utils {
        // Convert mangled constructor name to readable name
        std::string TranslateConstructorName(const std::string& mangledName);

        // Convert mangled destructor name to readable name
        std::string TranslateDestructorName(const std::string& mangledName);

        // Convert mangled member function name to readable name
        std::string TranslateMemberFunctionName(const std::string& mangledName);

        // Extract class name from mangled name
        std::string ExtractClassName(const std::string& mangledName);

        // Check if name is a constructor (starts with ??0)
        bool IsConstructor(const std::string& mangledName);

        // Check if name is a destructor (starts with ??1)
        bool IsDestructor(const std::string& mangledName);
    }
}

// Macro definitions to map old mangled names to new function names
// This allows existing code to continue working while using readable names

// Class constructor mappings
#define ??0CashItemRemoteStore@@QEAA@XZ NameTranslation::CashItemRemoteStore_Constructor
#define ??0CBattleTournamentInfo@@QEAA@XZ NameTranslation::CBattleTournamentInfo_Constructor
#define ??0CGuildBattleController@@IEAA@XZ NameTranslation::CGuildBattleController_Constructor
#define ??0CRFCashItemDatabase@@QEAA@XZ NameTranslation::CRFCashItemDatabase_Constructor
#define ??0MonsterSFContDamageToleracne@@QEAA@XZ NameTranslation::MonsterSFContDamageToleracne_Constructor

// Structure constructor mappings
#define ??0_attack_count_result_zocl@@QEAA@XZ NameTranslation::attack_count_result_zocl_Constructor
#define ??0_ATTACK_DELAY_CHECKER@@QEAA@XZ NameTranslation::ATTACK_DELAY_CHECKER_Constructor
#define ??0_attack_force_result_zocl@@QEAA@XZ NameTranslation::attack_force_result_zocl_Constructor
#define ??0_attack_gen_result_zocl@@QEAA@XZ NameTranslation::attack_gen_result_zocl_Constructor
#define ??0_attack_keeper_inform_zocl@@QEAA@XZ NameTranslation::attack_keeper_inform_zocl_Constructor
#define ??0_attack_param@@QEAA@XZ NameTranslation::attack_param_Constructor
#define ??0_attack_selfdestruction_result_zocl@@QEAA@XZ NameTranslation::attack_selfdestruction_result_zocl_Constructor
#define ??0_attack_siege_result_zocl@@QEAA@XZ NameTranslation::attack_siege_result_zocl_Constructor
#define ??0_attack_trap_inform_zocl@@QEAA@XZ NameTranslation::attack_trap_inform_zocl_Constructor
#define ??0_attack_unit_result_zocl@@QEAA@XZ NameTranslation::attack_unit_result_zocl_Constructor
#define ??0_be_damaged_char@@QEAA@XZ NameTranslation::be_damaged_char_Constructor
#define ??0_guild_battle_suggest_matter@@QEAA@XZ NameTranslation::guild_battle_suggest_matter_Constructor
#define ??0_param_cashitem_dblog@@QEAA@K@Z NameTranslation::param_cashitem_dblog_Constructor
#define ??0_personal_automine_attacked_zocl@@QEAA@XZ NameTranslation::personal_automine_attacked_zocl_Constructor
#define ??0_possible_battle_guild_list_result_zocl@@QEAA@XZ NameTranslation::possible_battle_guild_list_result_zocl_Constructor

// Documentation for developers
/*
 * USAGE INSTRUCTIONS:
 *
 * 1. Include this header in files that need to use RF Online functions
 * 2. Use the readable function names (e.g., CashItemRemoteStore_Constructor)
 * 3. The macros will automatically map old mangled names to new names
 * 4. Original decompiled code remains unchanged in the decompiled/ directory
 * 5. This translation layer provides a clean interface for development
 *
 * ADDING NEW TRANSLATIONS:
 *
 * 1. Add forward declaration for the class/structure
 * 2. Add inline function in NameTranslation namespace
 * 3. Add macro mapping at the bottom of this file
 * 4. Follow the naming convention: ClassName_FunctionName
 *
 * PRESERVING ORIGINAL CODE:
 *
 * - Original decompiled files in decompiled/ directory are never modified
 * - This translation layer acts as a bridge between decompiled code and buildable code
 * - Mangled names are preserved in comments for reference
 * - New code should use readable function names
 */