#pragma once

// Generated global functions for player category
// NexusPro Server

#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"
#include "../common/NameTranslation.h"

// Global function declarations - using translated names
// Original mangled names are preserved in NameTranslation.h
void CCharacter_Constructor(CCharacter *);
void CLevel_Constructor(_QWORD *a1);
void CMonsterSkillPool_Constructor(CMonsterSkillPool *);
void CMonsterSkill_Constructor(CMonsterSkill *);
void CPartyModeKillMonsterExpNotify_Constructor(CPartyModeKillMonsterExpNotify *);
void CPartyPlayer_Constructor(CPartyPlayer *);
void CPlayerDB_Constructor(CPlayerDB *);
void CPlayer_Constructor(CPlayer *);
void cStaticMember_Player_Constructor(cStaticMember_Player *);
void CUnmannedTraderSubClassInfoLevel_Constructor(CUnmannedTraderSubClassInfoLevel *, unsigned int dwID);
void SKILL_Constructor(SKILL *);
void attack_skill_result_zocl_Constructor(_attack_skill_result_zocl *);
void be_damaged_player_Constructor(_be_damaged_player *);
void character_create_setdata_Constructor(_character_create_setdata *);
void character_db_load_Constructor(_character_db_load *);
void dh_player_mgr_Constructor(_dh_player_mgr *);
void nuclear_bomb_explosion_result_zocl_Constructor(_nuclear_bomb_explosion_result_zocl *);
void nuclear_explosion_success_zocl_Constructor(_nuclear_explosion_success_zocl *);
void qry_case_character_rename_Constructor(_qry_case_character_rename *);
void SKILL_IDX_PER_MASTERY_Constructor(_SKILL_IDX_PER_MASTERY *);
void target_player_damage_contsf_allinform_zocl_Constructor(_target_player_damage_contsf_allinform_zocl *);
void ??0_throw_skill_result_one_zocl@@QEAA@XZ(_throw_skill_result_one_zocl *);
void ??0_throw_skill_result_other_zocl@@QEAA@XZ(_throw_skill_result_other_zocl *);
void ??1CCharacter@@UEAA@XZ(CCharacter *);
int64_t ??1CLevel@@UEAA@XZ(CLevel *);
void ??1CMonsterSkillPool@@QEAA@XZ(CMonsterSkillPool *);
void ??1CMonsterSkill@@QEAA@XZ(CMonsterSkill *);
void ??1CPartyModeKillMonsterExpNotify@@QEAA@XZ(CPartyModeKillMonsterExpNotify *);
void ??1CPlayerDB@@QEAA@XZ(CPlayerDB *);
void ??1CPlayer@@UEAA@XZ(CPlayer *);
void ??1cStaticMember_Player@@AEAA@XZ(cStaticMember_Player *);
void ??1CUnmannedTraderSubClassInfoLevel@@QEAA@XZ(CUnmannedTraderSubClassInfoLevel *);
char ?AuthorityFilter@@YA_NPEAUCHEAT_COMMAND@@PEAVCPlayer@@@Z(CHEAT_COMMAND *pCmd, CPlayer *pOne);
char ?CreateAnimus@@YA_NPEAVCMapData@@GPEAMEHHKPEAVCPlayer@@@Z(CMapData *pMap, unsigned __int16 wLayer, float *fPos, char byClass, int nHP, int nFP, unsigned int dwExp, CPlayer *pMaster);
void ?CreateTrap@@YAPEAVCTrap@@PEAVCMapData@@GPEAMPEAVCPlayer@@H@Z(CMapData *pMap, unsigned __int16 wLayer, float *fPos, CPlayer *pMaster, int nTrapItemIndex);
char ?ct_action_point_set@@YA_NPEAVCPlayer@@@Z(CPlayer *pOne);
bool ?ct_add_guild_schedule@@YA_NPEAVCPlayer@@@Z(CPlayer *pOne);
bool ?ct_add_one_day_guild_schedule@@YA_NPEAVCPlayer@@@Z(CPlayer *pOne);
bool ?ct_all_item_muzi@@YA_NPEAVCPlayer@@@Z(CPlayer *pOne);
char ?ct_all_map@@YA_NPEAVCPlayer@@@Z(CPlayer *pOne);
bool ?ct_alter_cashbag@@YA_NPEAVCPlayer@@@Z(CPlayer *pOne);
bool ?ct_alter_dalant@@YA_NPEAVCPlayer@@@Z(CPlayer *pOne);
char ?ct_alter_exp@@YA_NPEAVCPlayer@@@Z(CPlayer *pOne);
bool ?ct_alter_gold@@YA_NPEAVCPlayer@@@Z(CPlayer *pOne);
bool ?ct_alter_inven_dur@@YA_NPEAVCPlayer@@@Z(CPlayer *pOne);
bool ?ct_alter_lv@@YA_NPEAVCPlayer@@@Z(CPlayer *pOne);
bool ?ct_alter_pvp@@YA_NPEAVCPlayer@@@Z(CPlayer *pOne);
char ?ct_amp_full@@YA_NPEAVCPlayer@@@Z(CPlayer *pOne);
char ?ct_amp_set@@YA_NPEAVCPlayer@@@Z(CPlayer *pOne);
char ?ct_animusexp@@YA_NPEAVCPlayer@@@Z(CPlayer *pOne);
// ... and 301 more functions
