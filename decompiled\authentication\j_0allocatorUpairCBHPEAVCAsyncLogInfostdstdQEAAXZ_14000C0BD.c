/*
 * Function: j_??0?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@std@@QEAA@XZ
 * Address: 0x14000C0BD
 */

void __fastcall std::allocator<std::pair<int const,CAsyncLogInfo *>>::allocator<std::pair<int const,CAsyncLogInfo *>>(std::allocator<std::pair<int const ,CAsyncLogInfo *> > *this)
{
  std::allocator<std::pair<int const,CAsyncLogInfo *>>::allocator<std::pair<int const,CAsyncLogInfo *>>(this);
}
