/*
 * Function: _CMoveMapLimitRightInfo::LogIn_::_1_::dtor$1
 * Address: 0x1403AD0C0
 */

void __fastcall CMoveMapLimitRightInfo::LogIn_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>((std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)(a2 + 56));
}
