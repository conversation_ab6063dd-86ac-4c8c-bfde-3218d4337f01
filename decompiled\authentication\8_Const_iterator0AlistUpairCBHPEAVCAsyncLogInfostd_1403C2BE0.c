/*
 * Function: ??8?$_Const_iterator@$0A@@?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@QEBA_NAEBV012@@Z
 * Address: 0x1403C2BE0
 */

bool __fastcall std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Const_iterator<0>::operator==(std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Const_iterator<0> *this, std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Const_iterator<0> *_Right)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // [sp+0h] [bp-18h]@1
  std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Const_iterator<0> *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  return v6->_Ptr == _Right->_Ptr;
}
