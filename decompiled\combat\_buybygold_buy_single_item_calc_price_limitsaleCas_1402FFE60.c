/*
 * Function: ?_buybygold_buy_single_item_calc_price_limitsale@CashItemRemoteStore@@AEAAKHE@Z
 * Address: 0x1402FFE60
 */

__int64 __fastcall CashItemRemoteStore::_buybygold_buy_single_item_calc_price_limitsale(CashItemRemoteStore *this, int nCsPrice, char byOverlapNum)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  CashItemRemoteStore *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  return (unsigned int)(unsigned __int8)byOverlapNum
       * (nCsPrice
        - 10
        * (signed int)floor((double)((unsigned __int8)CashItemRemoteStore::GetLimDiscout(v7) * nCsPrice / 100) * 0.1 + 0.5));
}
