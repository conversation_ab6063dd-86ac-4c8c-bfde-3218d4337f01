/*
 * Function: ?update_cristalbattle_date@CRFWorldDatabase@@QEAA_NKE@Z
 * Address: 0x1404C5160
 */

bool __fastcall CRFWorldDatabase::update_cristalbattle_date(CRFWorldDatabase *this, unsigned int dwCharSerial, char bHSKTime)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-158h]@1
  char Dest; // [sp+30h] [bp-128h]@4
  unsigned __int64 v8; // [sp+140h] [bp-18h]@4
  CRFWorldDatabase *v9; // [sp+160h] [bp+8h]@1

  v9 = this;
  v3 = &v6;
  for ( i = 84i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = (unsigned __int64)&v6 ^ _security_cookie;
  sprintf(&Dest, "{ CALL pUpdate_CristalBattleDate( %d, %d ) }", dwCharSerial, (unsigned __int8)bHSKTime);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &Dest, 1);
}
