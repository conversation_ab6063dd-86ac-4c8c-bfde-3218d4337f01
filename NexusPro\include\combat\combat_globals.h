#pragma once

// Generated global functions for combat category
// NexusPro Server

#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"
#include "../common/NameTranslation.h"

// Global function declarations - using translated names
// Original mangled names are preserved in NameTranslation.h
void CashItemRemoteStore_Constructor(CashItemRemoteStore *);
void CBattleTournamentInfo_Constructor(CBattleTournamentInfo *);
void CGuildBattleController_Constructor(CGuildBattleController *);
void CRFCashItemDatabase_Constructor(CRFCashItemDatabase *);
void MonsterSFContDamageToleracne_Constructor(MonsterSFContDamageToleracne *);
void attack_count_result_zocl_Constructor(_attack_count_result_zocl *);
void ATTACK_DELAY_CHECKER_Constructor(_ATTACK_DELAY_CHECKER *);
void attack_force_result_zocl_Constructor(_attack_force_result_zocl *);
void attack_gen_result_zocl_Constructor(_attack_gen_result_zocl *);
void attack_keeper_inform_zocl_Constructor(_attack_keeper_inform_zocl *);
void attack_param_Constructor(_attack_param *);
void attack_selfdestruction_result_zocl_Constructor(_attack_selfdestruction_result_zocl *);
void attack_siege_result_zocl_Constructor(_attack_siege_result_zocl *);
void attack_trap_inform_zocl_Constructor(_attack_trap_inform_zocl *);
void attack_unit_result_zocl_Constructor(_attack_unit_result_zocl *);
void be_damaged_char_Constructor(_be_damaged_char *);
void guild_battle_suggest_matter_Constructor(_guild_battle_suggest_matter *);
void param_cashitem_dblog_Constructor(_param_cashitem_dblog *, unsigned int dwAv);
void personal_automine_attacked_zocl_Constructor(_personal_automine_attacked_zocl *);
void possible_battle_guild_list_result_zocl_Constructor(_possible_battle_guild_list_result_zocl *);
// Forward declarations for combat classes
class CashItemRemoteStore;
class CBattleTournamentInfo;
class CGuildBattleController;
class CRFCashItemDatabase;
struct _param_cashitem_dblog;

// Combat system destructors and utility functions
void CashItemRemoteStore_Destructor(CashItemRemoteStore *pThis);
void CBattleTournamentInfo_Destructor(CBattleTournamentInfo *pThis);
void CGuildBattleController_Destructor(CGuildBattleController *pThis);
void CRFCashItemDatabase_Destructor(CRFCashItemDatabase *pThis);
void param_cashitem_dblog_Destructor(_param_cashitem_dblog *pThis);

// Combat utility functions
int64_t IsCashItem(char byTblCode, unsigned int dwIndex);
int64_t IsStorageCodeWithItemKind(int nTableCode, int nStorageCode);

// Combat system cleanup functions
void CGuildBattleController_Cleanup(CGuildBattleController *pThis, int flags);
void CRFCashItemDatabase_Cleanup(CRFCashItemDatabase *pThis, int flags);
