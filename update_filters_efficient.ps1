#!/usr/bin/env powershell

# Efficient script to update NexusPro.vcxproj.filters
Write-Host "Updating NexusPro.vcxproj.filters efficiently..." -ForegroundColor Green

$projectRoot = "NexusPro"
$filtersPath = "$projectRoot\NexusPro.vcxproj.filters"

# Define all known system directories
$systemDirs = @(
    "authentication", "combat", "common", "database", "economy", 
    "items", "network", "player", "security", "system", "world"
)

Write-Host "Creating filter definitions for $($systemDirs.Count) systems..." -ForegroundColor Yellow

# Generate filter definitions
$filterDefs = @()
$filterDefs += "    <!-- Filter Definitions -->"
$filterDefs += "    <Filter Include=`"Source Files`">"
$filterDefs += "      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>"
$filterDefs += "      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>"
$filterDefs += "    </Filter>"
$filterDefs += "    <Filter Include=`"Header Files`">"
$filterDefs += "      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>"
$filterDefs += "      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>"
$filterDefs += "    </Filter>"
$filterDefs += "    <Filter Include=`"Resource Files`">"
$filterDefs += "      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>"
$filterDefs += "      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>"
$filterDefs += "    </Filter>"
$filterDefs += ""

# Add source file filters
$filterDefs += "    <!-- Source File Folders -->"
$guidCounter = 942
foreach ($dir in $systemDirs) {
    $dirName = (Get-Culture).TextInfo.ToTitleCase($dir.ToLower())
    $filterDefs += "    <Filter Include=`"Source Files\$dirName`">"
    $filterDefs += "      <UniqueIdentifier>{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC$guidCounter}</UniqueIdentifier>"
    $filterDefs += "    </Filter>"
    $guidCounter++
}
$filterDefs += ""

# Add header file filters
$filterDefs += "    <!-- Header File Folders -->"
$guidCounter = 960
foreach ($dir in $systemDirs) {
    $dirName = (Get-Culture).TextInfo.ToTitleCase($dir.ToLower())
    $filterDefs += "    <Filter Include=`"Header Files\$dirName`">"
    $filterDefs += "      <UniqueIdentifier>{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC$guidCounter}</UniqueIdentifier>"
    $filterDefs += "    </Filter>"
    $guidCounter++
}

Write-Host "Scanning and organizing source files..." -ForegroundColor Yellow

# Function to get files efficiently and organize them
function Get-OrganizedFiles {
    param($BasePath, $Extension, $ItemType)
    
    $organizedFiles = @{}
    
    foreach ($dir in $systemDirs) {
        $dirPath = Join-Path $BasePath $dir
        if (Test-Path $dirPath) {
            $files = Get-ChildItem -Path $dirPath -Filter "*.$Extension" -Recurse | ForEach-Object {
                $_.FullName.Replace((Get-Location).Path + "\", "").Replace("/", "\")
            }
            if ($files.Count -gt 0) {
                $organizedFiles[$dir] = $files
            }
        }
    }
    
    return $organizedFiles
}

# Get organized source files
$sourceFiles = Get-OrganizedFiles "$projectRoot\src" "cpp" "ClCompile"
$headerFiles = Get-OrganizedFiles "$projectRoot\include" "h" "ClInclude"

Write-Host "Found source files in $($sourceFiles.Keys.Count) directories" -ForegroundColor Cyan
Write-Host "Found header files in $($headerFiles.Keys.Count) directories" -ForegroundColor Cyan

# Generate source file assignments efficiently
$sourceAssignments = @()
$sourceAssignments += "  <!-- Source File Assignments -->"
$sourceAssignments += "  <ItemGroup>"

foreach ($dir in $systemDirs) {
    if ($sourceFiles.ContainsKey($dir)) {
        $dirName = (Get-Culture).TextInfo.ToTitleCase($dir.ToLower())
        $sourceAssignments += "    <!-- $dirName Sources -->"
        
        # Process files in batches to avoid memory issues
        $files = $sourceFiles[$dir] | Sort-Object
        foreach ($file in $files) {
            $sourceAssignments += "    <ClCompile Include=`"$file`">"
            $sourceAssignments += "      <Filter>Source Files\$dirName</Filter>"
            $sourceAssignments += "    </ClCompile>"
        }
        $sourceAssignments += ""
        Write-Host "  Added $($files.Count) source files for $dirName" -ForegroundColor Gray
    }
}

$sourceAssignments += "  </ItemGroup>"

Write-Host "Scanning and organizing header files..." -ForegroundColor Yellow

# Generate header file assignments efficiently
$headerAssignments = @()
$headerAssignments += "  <!-- Header File Assignments -->"
$headerAssignments += "  <ItemGroup>"

foreach ($dir in $systemDirs) {
    if ($headerFiles.ContainsKey($dir)) {
        $dirName = (Get-Culture).TextInfo.ToTitleCase($dir.ToLower())
        $headerAssignments += "    <!-- $dirName Headers -->"
        
        # Process files in batches to avoid memory issues
        $files = $headerFiles[$dir] | Sort-Object
        foreach ($file in $files) {
            $headerAssignments += "    <ClInclude Include=`"$file`">"
            $headerAssignments += "      <Filter>Header Files\$dirName</Filter>"
            $headerAssignments += "    </ClInclude>"
        }
        $headerAssignments += ""
        Write-Host "  Added $($files.Count) header files for $dirName" -ForegroundColor Gray
    }
}

$headerAssignments += "  </ItemGroup>"

Write-Host "Generating final filters file..." -ForegroundColor Green

# Create the new filters content
$newFiltersContent = @"
<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
$($filterDefs -join "`r`n")
  </ItemGroup>
  
$($sourceAssignments -join "`r`n")
  
$($headerAssignments -join "`r`n")
  
</Project>
"@

# Backup original file
Copy-Item $filtersPath "$filtersPath.backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"

# Write new filters file
$newFiltersContent | Out-File -FilePath $filtersPath -Encoding UTF8

$totalSourceFiles = ($sourceFiles.Values | ForEach-Object { $_.Count } | Measure-Object -Sum).Sum
$totalHeaderFiles = ($headerFiles.Values | ForEach-Object { $_.Count } | Measure-Object -Sum).Sum

Write-Host "Filters file updated successfully!" -ForegroundColor Green
Write-Host "Organized $totalSourceFiles source files and $totalHeaderFiles header files" -ForegroundColor Cyan
Write-Host "Files organized into $($systemDirs.Count) system categories" -ForegroundColor Cyan
Write-Host "Original file backed up as $filtersPath.backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')" -ForegroundColor Yellow
