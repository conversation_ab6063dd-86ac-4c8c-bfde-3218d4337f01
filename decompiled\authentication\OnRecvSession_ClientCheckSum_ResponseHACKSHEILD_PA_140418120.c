/*
 * Function: ?OnRecvSession_ClientCheckSum_Response@HACKSHEILD_PARAM_ANTICP@@QEAA_N_KPEAD@Z
 * Address: 0x140418120
 */

char __fastcall HACKSHEILD_PARAM_ANTICP::OnRecvSession_ClientCheckSum_Response(HACKSHEILD_PARAM_ANTICP *this, unsigned __int64 tSize, char *pMsg)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-88h]@1
  unsigned __int16 nLen; // [sp+20h] [bp-68h]@13
  char szMsg[2]; // [sp+34h] [bp-54h]@13
  char *v9; // [sp+48h] [bp-40h]@11
  unsigned int dwRet; // [sp+50h] [bp-38h]@11
  char pbyType; // [sp+64h] [bp-24h]@13
  char v12; // [sp+65h] [bp-23h]@13
  char *v13; // [sp+78h] [bp-10h]@11
  HACKSHEILD_PARAM_ANTICP *v14; // [sp+90h] [bp+8h]@1

  v14 = this;
  v3 = &v6;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( tSize == 342 )
  {
    if ( v14->m_nSocketIndex >= 0 && v14->m_nSocketIndex < 2532 )
    {
      if ( v14->m_byVerifyState == 1 )
      {
        v9 = pMsg;
        v13 = pMsg + 2;
        dwRet = _AntiCpSvr_AnalyzeGuidAckMsg(pMsg + 2, v14->m_byGUIDClientInfo, &v14->m_CrcInfo);
        if ( dwRet )
        {
          HACKSHEILD_PARAM_ANTICP::Kick(v14, 2, dwRet);
          result = 1;
        }
        else
        {
          *(_WORD *)szMsg = 0;
          pbyType = 98;
          v12 = 6;
          nLen = 2;
          CNetProcess::LoadSendMsg(unk_1414F2088, v14->m_nSocketIndex, &pbyType, szMsg, 2u);
          v14->m_byVerifyState = 2;
          result = 1;
        }
      }
      else
      {
        HACKSHEILD_PARAM_ANTICP::Kick(v14, 2, 0xFFFFFFFF);
        result = 1;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
