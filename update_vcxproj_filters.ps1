#!/usr/bin/env powershell

# Script to update NexusPro.vcxproj.filters with all source files
Write-Host "Updating NexusPro.vcxproj.filters file..." -ForegroundColor Green

$projectRoot = "NexusPro"
$filtersPath = "$projectRoot\NexusPro.vcxproj.filters"
$includePath = "$projectRoot\include"
$srcPath = "$projectRoot\src"

# Scan header files
Write-Host "Scanning header files..." -ForegroundColor Yellow
$headerFiles = @()
if (Test-Path $includePath) {
    Get-ChildItem -Path $includePath -Recurse -Filter "*.h" | ForEach-Object {
        $relativePath = $_.FullName.Replace((Get-Location).Path + "\", "").Replace("/", "\")
        $headerFiles += $relativePath
    }
}

# Scan source files  
Write-Host "Scanning source files..." -ForegroundColor Yellow
$sourceFiles = @()
if (Test-Path $srcPath) {
    Get-ChildItem -Path $srcPath -Recurse -Filter "*.cpp" | ForEach-Object {
        $relativePath = $_.FullName.Replace((Get-Location).Path + "\", "").Replace("/", "\")
        $sourceFiles += $relativePath
    }
}

Write-Host "Found $($headerFiles.Count) header files" -ForegroundColor Cyan
Write-Host "Found $($sourceFiles.Count) source files" -ForegroundColor Cyan

# Get unique directories for filters
$sourceDirs = $sourceFiles | ForEach-Object { 
    $parts = $_.Split('\')
    if ($parts.Length -gt 2) { $parts[2] } else { "main" }
} | Sort-Object -Unique

$headerDirs = $headerFiles | ForEach-Object { 
    $parts = $_.Split('\')
    if ($parts.Length -gt 2) { $parts[2] } else { "common" }
} | Sort-Object -Unique

Write-Host "Generating updated filters file..." -ForegroundColor Green

# Generate filter definitions
$filterDefs = @()
$filterDefs += "    <!-- Filter Definitions -->"
$filterDefs += "    <Filter Include=`"Source Files`">"
$filterDefs += "      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>"
$filterDefs += "      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>"
$filterDefs += "    </Filter>"
$filterDefs += "    <Filter Include=`"Header Files`">"
$filterDefs += "      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>"
$filterDefs += "      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>"
$filterDefs += "    </Filter>"
$filterDefs += "    <Filter Include=`"Resource Files`">"
$filterDefs += "      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>"
$filterDefs += "      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>"
$filterDefs += "    </Filter>"
$filterDefs += ""

# Add source file filters
$filterDefs += "    <!-- Source File Folders -->"
$guidCounter = 942
foreach ($dir in $sourceDirs) {
    $dirName = (Get-Culture).TextInfo.ToTitleCase($dir.ToLower())
    $filterDefs += "    <Filter Include=`"Source Files\$dirName`">"
    $filterDefs += "      <UniqueIdentifier>{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC$guidCounter}</UniqueIdentifier>"
    $filterDefs += "    </Filter>"
    $guidCounter++
}
$filterDefs += ""

# Add header file filters
$filterDefs += "    <!-- Header File Folders -->"
$guidCounter = 950
foreach ($dir in $headerDirs) {
    $dirName = (Get-Culture).TextInfo.ToTitleCase($dir.ToLower())
    $filterDefs += "    <Filter Include=`"Header Files\$dirName`">"
    $filterDefs += "      <UniqueIdentifier>{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC$guidCounter}</UniqueIdentifier>"
    $filterDefs += "    </Filter>"
    $guidCounter++
}

# Generate source file assignments
$sourceAssignments = @()
$sourceAssignments += "  <!-- Source File Assignments -->"
$sourceAssignments += "  <ItemGroup>"

$sourcesByDir = $sourceFiles | Group-Object { 
    $parts = $_.Split('\')
    if ($parts.Length -gt 2) { $parts[2] } else { "main" }
}

foreach ($group in $sourcesByDir) {
    $dirName = (Get-Culture).TextInfo.ToTitleCase($group.Name.ToLower())
    $sourceAssignments += "    <!-- $dirName Sources -->"
    foreach ($file in $group.Group | Sort-Object) {
        $sourceAssignments += "    <ClCompile Include=`"$file`">"
        $sourceAssignments += "      <Filter>Source Files\$dirName</Filter>"
        $sourceAssignments += "    </ClCompile>"
    }
    $sourceAssignments += ""
}

$sourceAssignments += "  </ItemGroup>"

# Generate header file assignments
$headerAssignments = @()
$headerAssignments += "  <!-- Header File Assignments -->"
$headerAssignments += "  <ItemGroup>"

$headersByDir = $headerFiles | Group-Object { 
    $parts = $_.Split('\')
    if ($parts.Length -gt 2) { $parts[2] } else { "common" }
}

foreach ($group in $headersByDir) {
    $dirName = (Get-Culture).TextInfo.ToTitleCase($group.Name.ToLower())
    $headerAssignments += "    <!-- $dirName Headers -->"
    foreach ($file in $group.Group | Sort-Object) {
        $headerAssignments += "    <ClInclude Include=`"$file`">"
        $headerAssignments += "      <Filter>Header Files\$dirName</Filter>"
        $headerAssignments += "    </ClInclude>"
    }
    $headerAssignments += ""
}

$headerAssignments += "  </ItemGroup>"

# Create the new filters content
$newFiltersContent = @"
<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
$($filterDefs -join "`r`n")
  </ItemGroup>
  
$($sourceAssignments -join "`r`n")
  
$($headerAssignments -join "`r`n")
  
</Project>
"@

# Backup original file
Copy-Item $filtersPath "$filtersPath.backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"

# Write new filters file
$newFiltersContent | Out-File -FilePath $filtersPath -Encoding UTF8

Write-Host "Filters file updated successfully!" -ForegroundColor Green
Write-Host "Added filters for $($headerFiles.Count) header files and $($sourceFiles.Count) source files" -ForegroundColor Cyan
Write-Host "Original file backed up as $filtersPath.backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')" -ForegroundColor Yellow
