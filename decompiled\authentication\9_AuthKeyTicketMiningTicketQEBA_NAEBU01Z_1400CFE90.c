/*
 * Function: ??9_AuthKeyTicket@MiningTicket@@QEBA_NAEBU01@@Z
 * Address: 0x1400CFE90
 */

bool __fastcall MiningTicket::_AuthKeyTicket::operator!=(MiningTicket::_AuthKeyTicket *this, MiningTicket::_AuthKeyTicket *Src)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // [sp+0h] [bp-18h]@1
  MiningTicket::_AuthKeyTicket *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  return v6->uiData != Src->uiData;
}
