/*
 * Function: ?D3D_R3InvalidateDevice@@YAJXZ
 * Address: 0x14050B040
 */

__int64 D3D_R3InvalidateDevice(void)
{
  CN_InvalidateNature();
  ReleaseVertexShaderList();
  ReleaseBlurVBuffer();
  ReleaseFullScreenEffect();
  if ( stOldRenderTarget )
  {
    ((void (*)(void))stOldRenderTarget->vfptr->Release)();
    stOldRenderTarget = 0i64;
  }
  if ( stOldStencilZ )
  {
    ((void (*)(void))stOldStencilZ->vfptr->Release)();
    stOldStencilZ = 0i64;
  }
  return 0i64;
}
