/*
 * Function: ?_CalcForceAttPnt@CAttack@@IEAAH_N@Z
 * Address: 0x14016AF70
 */

__int64 __fastcall CAttack::_CalcForceAttPnt(CAttack *this, bool bUseEffBullet)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@8
  float v5; // xmm0_4@11
  float v6; // xmm0_4@11
  int v7; // eax@13
  float v8; // xmm0_4@16
  float v9; // xmm1_4@16
  int v10; // eax@18
  __int64 v11; // [sp+0h] [bp-78h]@1
  _base_fld *v12; // [sp+20h] [bp-58h]@4
  float v13; // [sp+28h] [bp-50h]@4
  float v14; // [sp+2Ch] [bp-4Ch]@4
  int v15; // [sp+30h] [bp-48h]@4
  int v16; // [sp+34h] [bp-44h]@4
  int v17; // [sp+38h] [bp-40h]@7
  int v18; // [sp+3Ch] [bp-3Ch]@11
  int v19; // [sp+40h] [bp-38h]@11
  int v20; // [sp+58h] [bp-20h]@11
  int v21; // [sp+5Ch] [bp-1Ch]@16
  float v22; // [sp+64h] [bp-14h]@11
  float v23; // [sp+68h] [bp-10h]@16
  CAttack *v24; // [sp+80h] [bp+8h]@1

  v24 = this;
  v2 = &v11;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = v24->m_pp->pFld;
  v13 = (float)v24->m_pp->nLevel + (float)((float)(7.0 - (float)v24->m_pp->nLevel) * 0.5);
  v14 = *(float *)&v12[11].m_strCode[8];
  v15 = 0;
  v16 = 0;
  if ( bUseEffBullet )
  {
    v15 = (signed int)floor((float)((float)((float)v24->m_pp->nMinAFPlus
                                          * (float)((float)(fR_0 + (float)((float)(v13 / 7.0) * fRLf_0))
                                                  + (float)((float)((float)v24->m_pp->nMastery / 99.0) * fRMf_0)))
                                  * v14) + 0.5);
    v16 = (signed int)floor((float)((float)((float)v24->m_pp->nMaxAFPlus
                                          * (float)((float)(fR_0 + (float)((float)(v13 / 7.0) * fRLf_0))
                                                  + (float)((float)((float)v24->m_pp->nMastery / 99.0) * fRMf_0)))
                                  * v14) + 0.5);
  }
  else
  {
    v15 = (signed int)floor((float)((float)((float)v24->m_pp->nMinAF
                                          * (float)((float)(fR_0 + (float)((float)(v13 / 7.0) * fRLf_0))
                                                  + (float)((float)((float)v24->m_pp->nMastery / 99.0) * fRMf_0)))
                                  * v14) + 0.5);
    v16 = (signed int)floor((float)((float)((float)v24->m_pp->nMaxAF
                                          * (float)((float)(fR_0 + (float)((float)(v13 / 7.0) * fRLf_0))
                                                  + (float)((float)((float)v24->m_pp->nMastery / 99.0) * fRMf_0)))
                                  * v14) + 0.5);
  }
  v17 = (signed int)floor((double)((v16 + 125) / (v16 + 50) * v16) + 0.5);
  if ( v24->m_pp->nMaxAttackPnt <= 0 )
  {
    if ( v24->m_pp->nMaxAttackPnt >= 0 )
    {
      v18 = (signed int)floor((float)((float)(v16 + v15) / 2.0) + 0.5);
      v19 = _100_per_random_table::GetRand(&v24->m_pAttChar->m_rtPer100);
      v22 = (float)v24->m_pp->nMinSel;
      v5 = v22;
      _effect_parameter::GetEff_Plus(&v24->m_pAttChar->m_EP, 14);
      v6 = v22 - v5;
      v20 = (signed int)ffloor(v6);
      if ( v24->m_pp->pDst && v24->m_pp->pDst != v24->m_pAttChar )
      {
        _effect_parameter::GetEff_Plus(&v24->m_pp->pDst->m_EP, 37);
        v20 = (signed int)ffloor((float)v20 + v6);
        v7 = CAttack::MonsterCritical_Exception_Rate(v24, v24->m_pp->pDst, v24->m_pp->bBackAttack);
        v20 += v7;
      }
      if ( v20 < 0 )
        v20 = 0;
      v23 = (float)(v24->m_pp->nMaxSel + v24->m_pp->nMinSel);
      v8 = v23;
      _effect_parameter::GetEff_Plus(&v24->m_pAttChar->m_EP, 14);
      v9 = v23 - v8;
      v21 = (signed int)ffloor(v23 - v8);
      if ( v24->m_pp->pDst && v24->m_pp->pDst != v24->m_pAttChar )
      {
        _effect_parameter::GetEff_Plus(&v24->m_pp->pDst->m_EP, 37);
        v21 = (signed int)ffloor((float)v21 + v9);
        v10 = CAttack::MonsterCritical_Exception_Rate(v24, v24->m_pp->pDst, v24->m_pp->bBackAttack);
        v21 += v10;
      }
      if ( v21 < 0 )
        v21 = 0;
      if ( v19 >= v20 )
      {
        if ( v19 >= v21 )
        {
          v24->m_bIsCrtAtt = 1;
          result = (unsigned int)v17;
        }
        else if ( v16 - v18 <= 0 )
        {
          result = (unsigned int)v18;
        }
        else
        {
          result = (unsigned int)(rand() % (v16 - v18) + v18);
        }
      }
      else if ( v18 - v15 <= 0 )
      {
        result = (unsigned int)v15;
      }
      else
      {
        result = (unsigned int)(rand() % (v18 - v15) + v15);
      }
    }
    else
    {
      result = (unsigned int)v15;
    }
  }
  else
  {
    result = (unsigned int)v17;
  }
  return result;
}
