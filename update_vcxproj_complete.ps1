#!/usr/bin/env powershell

# Script to update NexusPro.vcxproj with all source files
Write-Host "Scanning NexusPro project files..." -ForegroundColor Green

$projectRoot = "NexusPro"
$vcxprojPath = "$projectRoot\NexusPro.vcxproj"
$includePath = "$projectRoot\include"
$srcPath = "$projectRoot\src"

# Function to get relative path from project root
function Get-RelativePath {
    param($FullPath, $BasePath)
    $relativePath = $FullPath.Replace($BasePath, "").TrimStart('\').Replace("/", "\")
    return $relativePath
}

# Scan header files
Write-Host "Scanning header files..." -ForegroundColor Yellow
$headerFiles = @()
if (Test-Path $includePath) {
    Get-ChildItem -Path $includePath -Recurse -Filter "*.h" | ForEach-Object {
        $relativePath = $_.FullName.Replace((Get-Location).Path + "\", "").Replace("/", "\")
        $headerFiles += $relativePath
    }
}

# Scan source files
Write-Host "Scanning source files..." -ForegroundColor Yellow
$sourceFiles = @()
if (Test-Path $srcPath) {
    Get-ChildItem -Path $srcPath -Recurse -Filter "*.cpp" | ForEach-Object {
        $relativePath = $_.FullName.Replace((Get-Location).Path + "\", "").Replace("/", "\")
        $sourceFiles += $relativePath
    }
}

Write-Host "Found $($headerFiles.Count) header files" -ForegroundColor Cyan
Write-Host "Found $($sourceFiles.Count) source files" -ForegroundColor Cyan

# Read current project file
$projectContent = Get-Content $vcxprojPath -Raw

# Find the insertion points for headers and sources
$headerInsertPoint = $projectContent.IndexOf("  <!-- Header Files -->")
$sourceInsertPoint = $projectContent.IndexOf("  <!-- Source Files - MINIMAL BUILD ONLY -->")

if ($headerInsertPoint -eq -1 -or $sourceInsertPoint -eq -1) {
    Write-Error "Could not find insertion points in project file"
    exit 1
}

# Generate header file entries grouped by directory
$headerEntries = @()
$headersByDir = $headerFiles | Group-Object { Split-Path (Split-Path $_ -Parent) -Leaf }

foreach ($group in $headersByDir) {
    $dirName = $group.Name
    if ($dirName -eq "include") { $dirName = "common" }
    
    $headerEntries += "    <!-- $dirName Headers -->"
    foreach ($file in $group.Group | Sort-Object) {
        $headerEntries += "    <ClInclude Include=`"$file`" />"
    }
    $headerEntries += ""
}

# Generate source file entries grouped by directory  
$sourceEntries = @()
$sourcesByDir = $sourceFiles | Group-Object { Split-Path (Split-Path $_ -Parent) -Leaf }

foreach ($group in $sourcesByDir) {
    $dirName = $group.Name
    if ($dirName -eq "src") { $dirName = "main" }
    
    $sourceEntries += "    <!-- $dirName Sources -->"
    foreach ($file in $group.Group | Sort-Object) {
        $sourceEntries += "    <ClCompile Include=`"$file`" />"
    }
    $sourceEntries += ""
}

Write-Host "Generating updated project file..." -ForegroundColor Green

# Create the new project content
$newProjectContent = @"
<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  
  <PropertyGroup Label="Globals">
    <VCProjectVersion>17.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{B8F4A8E2-3D4C-4F5A-9B2E-1C8D7E6F9A0B}</ProjectGuid>
    <RootNamespace>NexusPro</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
    <ProjectName>NexusPro</ProjectName>
  </PropertyGroup>
  
  <Import Project="`$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  
  <PropertyGroup Condition="'`$(Configuration)|`$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
    <LanguageStandard>stdcpp20</LanguageStandard>
  </PropertyGroup>
  
  <PropertyGroup Condition="'`$(Configuration)|`$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <LanguageStandard>stdcpp20</LanguageStandard>
  </PropertyGroup>
  
  <Import Project="`$(VCTargetsPath)\Microsoft.Cpp.props" />
  
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  
  <ImportGroup Label="Shared">
  </ImportGroup>
  
  <ImportGroup Label="PropertySheets" Condition="'`$(Configuration)|`$(Platform)'=='Debug|x64'">
    <Import Project="`$(UserRootDir)\Microsoft.Cpp.`$(Platform).user.props" Condition="exists('`$(UserRootDir)\Microsoft.Cpp.`$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  
  <ImportGroup Label="PropertySheets" Condition="'`$(Configuration)|`$(Platform)'=='Release|x64'">
    <Import Project="`$(UserRootDir)\Microsoft.Cpp.`$(Platform).user.props" Condition="exists('`$(UserRootDir)\Microsoft.Cpp.`$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  
  <PropertyGroup Label="UserMacros" />
  
  <PropertyGroup Condition="'`$(Configuration)|`$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>`$(SolutionDir)bin\`$(Platform)\`$(Configuration)\</OutDir>
    <IntDir>`$(SolutionDir)obj\`$(Platform)\`$(Configuration)\</IntDir>
    <TargetName>NexusPro</TargetName>
  </PropertyGroup>
  
  <PropertyGroup Condition="'`$(Configuration)|`$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>`$(SolutionDir)bin\`$(Platform)\`$(Configuration)\</OutDir>
    <IntDir>`$(SolutionDir)obj\`$(Platform)\`$(Configuration)\</IntDir>
    <TargetName>NexusPro</TargetName>
  </PropertyGroup>
  
  <ItemDefinitionGroup Condition="'`$(Configuration)|`$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalIncludeDirectories>`$(ProjectDir)include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <TreatWarningAsError>false</TreatWarningAsError>
      <DisableSpecificWarnings>4996;4267;4244;%(DisableSpecificWarnings)</DisableSpecificWarnings>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>ws2_32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  
  <ItemDefinitionGroup Condition="'`$(Configuration)|`$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalIncludeDirectories>`$(ProjectDir)include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <TreatWarningAsError>false</TreatWarningAsError>
      <DisableSpecificWarnings>4996;4267;4244;%(DisableSpecificWarnings)</DisableSpecificWarnings>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>ws2_32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  
  <!-- Source Files -->
  <ItemGroup>
$($sourceEntries -join "`r`n")
  </ItemGroup>
  
  <!-- Header Files -->
  <ItemGroup>
$($headerEntries -join "`r`n")
  </ItemGroup>
  
  <Import Project="`$(VCTargetsPath)\Microsoft.Cpp.targets" />
  
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
  
</Project>
"@

# Backup original file
Copy-Item $vcxprojPath "$vcxprojPath.backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"

# Write new project file
$newProjectContent | Out-File -FilePath $vcxprojPath -Encoding UTF8

Write-Host "Project file updated successfully!" -ForegroundColor Green
Write-Host "Added $($headerFiles.Count) header files and $($sourceFiles.Count) source files" -ForegroundColor Cyan
Write-Host "Original file backed up as $vcxprojPath.backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')" -ForegroundColor Yellow
