<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <!-- Filter Definitions -->
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    
    <!-- Source File Folders -->
    <Filter Include="Source Files\Authentication">
      <UniqueIdentifier>{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\System">
      <UniqueIdentifier>{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC943}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Network">
      <UniqueIdentifier>{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC944}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Database">
      <UniqueIdentifier>{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC945}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Player">
      <UniqueIdentifier>{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC946}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\World">
      <UniqueIdentifier>{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC947}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Combat">
      <UniqueIdentifier>{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC948}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Economy">
      <UniqueIdentifier>{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC949}</UniqueIdentifier>
    </Filter>
    
    <!-- Header File Folders -->
    <Filter Include="Header Files\Common">
      <UniqueIdentifier>{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC950}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\Authentication">
      <UniqueIdentifier>{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC951}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\System">
      <UniqueIdentifier>{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC952}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\Network">
      <UniqueIdentifier>{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC953}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\Database">
      <UniqueIdentifier>{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC954}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\Player">
      <UniqueIdentifier>{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC955}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\World">
      <UniqueIdentifier>{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC956}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\Combat">
      <UniqueIdentifier>{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC957}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\Economy">
      <UniqueIdentifier>{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC958}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  
  <!-- Source File Assignments -->
  <ItemGroup>
    <ClCompile Include="src\main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>

    <!-- Authentication Core -->
    <ClCompile Include="src\authentication\CAsyncLogInfo.cpp">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>
    <ClCompile Include="src\authentication\CBilling.cpp">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>
    <ClCompile Include="src\authentication\CBillingID.cpp">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>
    <ClCompile Include="src\authentication\CBillingJP.cpp">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>
    <ClCompile Include="src\authentication\CBillingManager.cpp">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>
    <ClCompile Include="src\authentication\CBillingNULL.cpp">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>
    <ClCompile Include="src\authentication\CHackShieldExSystem.cpp">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>
    <ClCompile Include="src\authentication\MiningTicket.cpp">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>
    <ClCompile Include="src\authentication\authentication_globals.cpp">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>

    <!-- Authentication STL Iterators -->
    <ClCompile Include="src\authentication\AEBU12.cpp">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>
    <ClCompile Include="src\authentication\AEBV012.cpp">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>
    <ClCompile Include="src\authentication\AEBV120.cpp">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>
    <ClCompile Include="src\authentication\AEBV123.cpp">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>
    <ClCompile Include="src\authentication\AEBV342.cpp">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>
    <ClCompile Include="src\authentication\AEBU10.cpp">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>
    <ClCompile Include="src\authentication\AEBU32.cpp">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>
    <ClCompile Include="src\authentication\AEBV01.cpp">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>

    <!-- Authentication Support -->
    <ClCompile Include="src\authentication\HACKSHEILD_PARAM_ANTICP.cpp">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>
    <ClCompile Include="src\authentication\DL_PrivateKeyImpl_CryptoPP.cpp">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>
    <ClCompile Include="src\authentication\CallFunc_RFOnline_Auth_.cpp">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>
    <ClCompile Include="src\authentication\LogIn_.cpp">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>
    <ClCompile Include="src\authentication\W4ASYNC_LOG_TYPE.cpp">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>
    <ClCompile Include="src\authentication\_apex_send_login.cpp">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>

    <!-- Authentication STL Support -->
    <ClCompile Include="src\authentication\HPEAVCAsyncLogInfo.cpp">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>

    <!-- Authentication UI Support -->
    <ClCompile Include="src\authentication\CDialog.cpp">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>
    <ClCompile Include="src\authentication\CFormView.cpp">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>
    <ClCompile Include="src\authentication\Sky.cpp">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>
    <ClCompile Include="src\authentication\Sun.cpp">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>

    <!-- Authentication Misc -->
    <ClCompile Include="src\authentication\PEAU342.cpp">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>
    <ClCompile Include="src\authentication\V312.cpp">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>
    <ClCompile Include="src\authentication\_CAsyncLogInfo_.cpp">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>
    
    <!-- System -->
    <ClCompile Include="src\system\CMainThread.cpp">
      <Filter>Source Files\System</Filter>
    </ClCompile>
    <ClCompile Include="src\system\CActionPointSystemMgr.cpp">
      <Filter>Source Files\System</Filter>
    </ClCompile>
    <ClCompile Include="src\system\CBossMonsterScheduleSystem.cpp">
      <Filter>Source Files\System</Filter>
    </ClCompile>
    <ClCompile Include="src\system\CHolyStoneSystem.cpp">
      <Filter>Source Files\System</Filter>
    </ClCompile>
    <ClCompile Include="src\system\CNationSettingData.cpp">
      <Filter>Source Files\System</Filter>
    </ClCompile>
    <ClCompile Include="src\system\system_globals.cpp">
      <Filter>Source Files\System</Filter>
    </ClCompile>
    
    <!-- Network -->
    <ClCompile Include="src\network\CNetworkEX.cpp">
      <Filter>Source Files\Network</Filter>
    </ClCompile>
    <ClCompile Include="src\network\CNetSocket.cpp">
      <Filter>Source Files\Network</Filter>
    </ClCompile>
    <ClCompile Include="src\network\CNetWorking.cpp">
      <Filter>Source Files\Network</Filter>
    </ClCompile>
    <ClCompile Include="src\network\network_globals.cpp">
      <Filter>Source Files\Network</Filter>
    </ClCompile>
    
    <!-- Database -->
    <ClCompile Include="src\database\CUserDB.cpp">
      <Filter>Source Files\Database</Filter>
    </ClCompile>
    <ClCompile Include="src\database\CRFWorldDatabase.cpp">
      <Filter>Source Files\Database</Filter>
    </ClCompile>
    <ClCompile Include="src\database\CRFNewDatabase.cpp">
      <Filter>Source Files\Database</Filter>
    </ClCompile>
    <ClCompile Include="src\database\database_globals.cpp">
      <Filter>Source Files\Database</Filter>
    </ClCompile>
    
    <!-- Player -->
    <ClCompile Include="src\player\CPlayer.cpp">
      <Filter>Source Files\Player</Filter>
    </ClCompile>
    <ClCompile Include="src\player\CCharacter.cpp">
      <Filter>Source Files\Player</Filter>
    </ClCompile>
    <ClCompile Include="src\player\CGameObject.cpp">
      <Filter>Source Files\Player</Filter>
    </ClCompile>
    <ClCompile Include="src\player\player_globals.cpp">
      <Filter>Source Files\Player</Filter>
    </ClCompile>
    
    <!-- World -->
    <ClCompile Include="src\world\CMonster.cpp">
      <Filter>Source Files\World</Filter>
    </ClCompile>
    <ClCompile Include="src\world\CMapData.cpp">
      <Filter>Source Files\World</Filter>
    </ClCompile>
    <ClCompile Include="src\world\world_globals.cpp">
      <Filter>Source Files\World</Filter>
    </ClCompile>
    
    <!-- Combat -->
    <ClCompile Include="src\combat\CAttack.cpp">
      <Filter>Source Files\Combat</Filter>
    </ClCompile>
    <ClCompile Include="src\combat\CGuildBattleController.cpp">
      <Filter>Source Files\Combat</Filter>
    </ClCompile>
    <ClCompile Include="src\combat\combat_globals.cpp">
      <Filter>Source Files\Combat</Filter>
    </ClCompile>
    
    <!-- Economy -->
    <ClCompile Include="src\economy\CMoneySupplyMgr.cpp">
      <Filter>Source Files\Economy</Filter>
    </ClCompile>
    <ClCompile Include="src\economy\CMgrGuildHistory.cpp">
      <Filter>Source Files\Economy</Filter>
    </ClCompile>
    <ClCompile Include="src\economy\economy_globals.cpp">
      <Filter>Source Files\Economy</Filter>
    </ClCompile>
    <ClCompile Include="src\economy\GuildCreateEventInfo.cpp">
      <Filter>Source Files\Economy</Filter>
    </ClCompile>
  </ItemGroup>
  
  <!-- Header File Assignments -->
  <ItemGroup>
    <!-- Common Headers -->
    <ClInclude Include="include\common\WindowsTypes.h">
      <Filter>Header Files\Common</Filter>
    </ClInclude>
    <ClInclude Include="include\common\Stubs.h">
      <Filter>Header Files\Common</Filter>
    </ClInclude>
    <ClInclude Include="include\common\Types.h">
      <Filter>Header Files\Common</Filter>
    </ClInclude>
    
    <!-- Authentication Core Headers -->
    <ClInclude Include="include\authentication\authentication_globals.h">
      <Filter>Header Files\Authentication</Filter>
    </ClInclude>
    <ClInclude Include="include\authentication\CAsyncLogInfo.h">
      <Filter>Header Files\Authentication</Filter>
    </ClInclude>
    <ClInclude Include="include\authentication\CBilling.h">
      <Filter>Header Files\Authentication</Filter>
    </ClInclude>
    <ClInclude Include="include\authentication\CBillingID.h">
      <Filter>Header Files\Authentication</Filter>
    </ClInclude>
    <ClInclude Include="include\authentication\CBillingJP.h">
      <Filter>Header Files\Authentication</Filter>
    </ClInclude>
    <ClInclude Include="include\authentication\CBillingManager.h">
      <Filter>Header Files\Authentication</Filter>
    </ClInclude>
    <ClInclude Include="include\authentication\CBillingNULL.h">
      <Filter>Header Files\Authentication</Filter>
    </ClInclude>
    <ClInclude Include="include\authentication\CHackShieldExSystem.h">
      <Filter>Header Files\Authentication</Filter>
    </ClInclude>
    <ClInclude Include="include\authentication\MiningTicket.h">
      <Filter>Header Files\Authentication</Filter>
    </ClInclude>

    <!-- Authentication STL Iterator Headers -->
    <ClInclude Include="include\authentication\AEBU12.h">
      <Filter>Header Files\Authentication</Filter>
    </ClInclude>
    <ClInclude Include="include\authentication\AEBV012.h">
      <Filter>Header Files\Authentication</Filter>
    </ClInclude>
    <ClInclude Include="include\authentication\AEBV120.h">
      <Filter>Header Files\Authentication</Filter>
    </ClInclude>
    <ClInclude Include="include\authentication\AEBV123.h">
      <Filter>Header Files\Authentication</Filter>
    </ClInclude>
    <ClInclude Include="include\authentication\AEBV342.h">
      <Filter>Header Files\Authentication</Filter>
    </ClInclude>
    <ClInclude Include="include\authentication\AEBU10.h">
      <Filter>Header Files\Authentication</Filter>
    </ClInclude>
    <ClInclude Include="include\authentication\AEBU32.h">
      <Filter>Header Files\Authentication</Filter>
    </ClInclude>
    <ClInclude Include="include\authentication\AEBV01.h">
      <Filter>Header Files\Authentication</Filter>
    </ClInclude>

    <!-- Authentication Support Headers -->
    <ClInclude Include="include\authentication\HACKSHEILD_PARAM_ANTICP.h">
      <Filter>Header Files\Authentication</Filter>
    </ClInclude>
    <ClInclude Include="include\authentication\DL_PrivateKeyImpl_CryptoPP.h">
      <Filter>Header Files\Authentication</Filter>
    </ClInclude>
    <ClInclude Include="include\authentication\CallFunc_RFOnline_Auth_.h">
      <Filter>Header Files\Authentication</Filter>
    </ClInclude>
    <ClInclude Include="include\authentication\LogIn_.h">
      <Filter>Header Files\Authentication</Filter>
    </ClInclude>
    <ClInclude Include="include\authentication\W4ASYNC_LOG_TYPE.h">
      <Filter>Header Files\Authentication</Filter>
    </ClInclude>
    <ClInclude Include="include\authentication\_apex_send_login.h">
      <Filter>Header Files\Authentication</Filter>
    </ClInclude>

    <!-- Authentication STL Support Headers -->
    <ClInclude Include="include\authentication\HPEAVCAsyncLogInfo.h">
      <Filter>Header Files\Authentication</Filter>
    </ClInclude>

    <!-- Authentication UI Support Headers -->
    <ClInclude Include="include\authentication\CDialog.h">
      <Filter>Header Files\Authentication</Filter>
    </ClInclude>
    <ClInclude Include="include\authentication\CFormView.h">
      <Filter>Header Files\Authentication</Filter>
    </ClInclude>
    <ClInclude Include="include\authentication\Sky.h">
      <Filter>Header Files\Authentication</Filter>
    </ClInclude>
    <ClInclude Include="include\authentication\Sun.h">
      <Filter>Header Files\Authentication</Filter>
    </ClInclude>

    <!-- Authentication Misc Headers -->
    <ClInclude Include="include\authentication\PEAU342.h">
      <Filter>Header Files\Authentication</Filter>
    </ClInclude>
    <ClInclude Include="include\authentication\V312.h">
      <Filter>Header Files\Authentication</Filter>
    </ClInclude>
    <ClInclude Include="include\authentication\_CAsyncLogInfo_.h">
      <Filter>Header Files\Authentication</Filter>
    </ClInclude>
    
    <!-- System Headers -->
    <ClInclude Include="include\system\CMainThread.h">
      <Filter>Header Files\System</Filter>
    </ClInclude>
    <ClInclude Include="include\system\CActionPointSystemMgr.h">
      <Filter>Header Files\System</Filter>
    </ClInclude>
    <ClInclude Include="include\system\CBossMonsterScheduleSystem.h">
      <Filter>Header Files\System</Filter>
    </ClInclude>
    <ClInclude Include="include\system\CHolyStoneSystem.h">
      <Filter>Header Files\System</Filter>
    </ClInclude>
    <ClInclude Include="include\system\CNationSettingData.h">
      <Filter>Header Files\System</Filter>
    </ClInclude>
    <ClInclude Include="include\system\system_globals.h">
      <Filter>Header Files\System</Filter>
    </ClInclude>
    
    <!-- Network Headers -->
    <ClInclude Include="include\network\CNetworkEX.h">
      <Filter>Header Files\Network</Filter>
    </ClInclude>
    <ClInclude Include="include\network\CNetSocket.h">
      <Filter>Header Files\Network</Filter>
    </ClInclude>
    <ClInclude Include="include\network\CNetWorking.h">
      <Filter>Header Files\Network</Filter>
    </ClInclude>
    <ClInclude Include="include\network\network_globals.h">
      <Filter>Header Files\Network</Filter>
    </ClInclude>
    
    <!-- Database Headers -->
    <ClInclude Include="include\database\CUserDB.h">
      <Filter>Header Files\Database</Filter>
    </ClInclude>
    <ClInclude Include="include\database\CRFWorldDatabase.h">
      <Filter>Header Files\Database</Filter>
    </ClInclude>
    <ClInclude Include="include\database\CRFNewDatabase.h">
      <Filter>Header Files\Database</Filter>
    </ClInclude>
    <ClInclude Include="include\database\database_globals.h">
      <Filter>Header Files\Database</Filter>
    </ClInclude>
    
    <!-- Player Headers -->
    <ClInclude Include="include\player\CPlayer.h">
      <Filter>Header Files\Player</Filter>
    </ClInclude>
    <ClInclude Include="include\player\CCharacter.h">
      <Filter>Header Files\Player</Filter>
    </ClInclude>
    <ClInclude Include="include\player\CGameObject.h">
      <Filter>Header Files\Player</Filter>
    </ClInclude>
    <ClInclude Include="include\player\player_globals.h">
      <Filter>Header Files\Player</Filter>
    </ClInclude>
    
    <!-- World Headers -->
    <ClInclude Include="include\world\CMonster.h">
      <Filter>Header Files\World</Filter>
    </ClInclude>
    <ClInclude Include="include\world\CMapData.h">
      <Filter>Header Files\World</Filter>
    </ClInclude>
    <ClInclude Include="include\world\world_globals.h">
      <Filter>Header Files\World</Filter>
    </ClInclude>

    <!-- Combat Headers -->
    <ClInclude Include="include\combat\CAttack.h">
      <Filter>Header Files\Combat</Filter>
    </ClInclude>
    <ClInclude Include="include\combat\CGuildBattleController.h">
      <Filter>Header Files\Combat</Filter>
    </ClInclude>
    <ClInclude Include="include\combat\combat_globals.h">
      <Filter>Header Files\Combat</Filter>
    </ClInclude>

    <!-- Economy Headers -->
    <ClInclude Include="include\economy\CMoneySupplyMgr.h">
      <Filter>Header Files\Economy</Filter>
    </ClInclude>
    <ClInclude Include="include\economy\CMgrGuildHistory.h">
      <Filter>Header Files\Economy</Filter>
    </ClInclude>
    <ClInclude Include="include\economy\economy_globals.h">
      <Filter>Header Files\Economy</Filter>
    </ClInclude>
    <ClInclude Include="include\economy\GuildCreateEventInfo.h">
      <Filter>Header Files\Economy</Filter>
    </ClInclude>
  </ItemGroup>
</Project>
