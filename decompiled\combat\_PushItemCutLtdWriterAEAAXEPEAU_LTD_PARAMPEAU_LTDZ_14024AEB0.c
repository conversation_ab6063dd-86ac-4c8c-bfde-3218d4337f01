/*
 * Function: ?_PushItemCut@LtdWriter@@AEAAXEPEAU_LTD_PARAM@@PEAU_LTD@@@Z
 * Address: 0x14024AEB0
 */

void __fastcall LtdWriter::_PushItemCut(LtdWriter *this, char bySubLogType, _LTD_PARAM *pParam, _LTD *pl)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-58h]@1
  bool bExpend[8]; // [sp+20h] [bp-38h]@5
  char j; // [sp+30h] [bp-28h]@5
  unsigned __int8 k; // [sp+31h] [bp-27h]@11
  char l; // [sp+32h] [bp-26h]@18
  unsigned __int8 m; // [sp+33h] [bp-25h]@24
  unsigned __int8 n; // [sp+34h] [bp-24h]@30
  int v13; // [sp+38h] [bp-20h]@4
  int v14; // [sp+3Ch] [bp-1Ch]@8
  int v15; // [sp+40h] [bp-18h]@14
  int v16; // [sp+44h] [bp-14h]@21
  int v17; // [sp+48h] [bp-10h]@27
  int v18; // [sp+4Ch] [bp-Ch]@33
  LtdWriter *v19; // [sp+60h] [bp+8h]@1
  _LTD_PARAM *v20; // [sp+70h] [bp+18h]@1
  _LTD *pla; // [sp+78h] [bp+20h]@1

  pla = pl;
  v20 = pParam;
  v19 = this;
  v4 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v13 = (unsigned __int8)bySubLogType;
  switch ( bySubLogType )
  {
    case 0:
    case 0xA:
    case 0x14:
    case 0x50:
      bExpend[0] = 1;
      LtdWriter::_SetLtd(v19, pParam->m_pUserDB, pl, 1, 1);
      for ( j = 0; (unsigned __int8)j < (signed int)v20->m_DstItem.m_byNum; ++j )
      {
        if ( v20->m_DstItem.m_pByOverlapNum )
          v14 = *v20->m_DstItem.m_pByOverlapNum;
        else
          v14 = 1;
        LtdWriter::_SetItemInfo(v19, j, &v20->m_DstItem.m_pItems[(unsigned __int8)j], v14, &pla->m_ItemInfo, 0);
      }
      for ( k = 0; k < (signed int)v20->m_MtrItem1.m_byNum; ++k )
      {
        if ( v20->m_MtrItem1.m_pByOverlapNum )
          v15 = *v20->m_MtrItem1.m_pByOverlapNum;
        else
          v15 = 1;
        LtdWriter::_SetItemInfo(v19, k + j, &v20->m_MtrItem1.m_pItems[k], v15, &pla->m_ItemInfo, 0);
      }
      LtdWriter::_SetExpend(v19, v20->m_wszEtc, &pla->m_Expend);
      break;
    case 0x28:
    case 0x32:
    case 0x3C:
    case 0x46:
      bExpend[0] = 1;
      LtdWriter::_SetLtd(v19, pParam->m_pUserDB, pl, 1, 1);
      for ( l = 0; (unsigned __int8)l < (signed int)v20->m_DstItem.m_byNum; ++l )
      {
        if ( v20->m_DstItem.m_pByOverlapNum )
          v16 = *v20->m_DstItem.m_pByOverlapNum;
        else
          v16 = 1;
        LtdWriter::_SetItemInfo(v19, l, &v20->m_DstItem.m_pItems[(unsigned __int8)l], v16, &pla->m_ItemInfo, 0);
      }
      for ( m = 0; m < (signed int)v20->m_MtrItem1.m_byNum; ++m )
      {
        if ( v20->m_MtrItem1.m_pByOverlapNum )
          v17 = *v20->m_MtrItem1.m_pByOverlapNum;
        else
          v17 = 1;
        LtdWriter::_SetItemInfo(v19, m + l, &v20->m_MtrItem1.m_pItems[m], v17, &pla->m_ItemInfo, 0);
      }
      for ( n = 0; n < (signed int)v20->m_MtrItem2.m_byNum; ++n )
      {
        if ( v20->m_MtrItem2.m_pByOverlapNum )
          v18 = *v20->m_MtrItem2.m_pByOverlapNum;
        else
          v18 = 1;
        LtdWriter::_SetItemInfo(v19, n + m + l, &v20->m_MtrItem2.m_pItems[n], v18, &pla->m_ItemInfo, 0);
      }
      LtdWriter::_SetExpend(v19, v20->m_wszEtc, &pla->m_Expend);
      break;
    default:
      return;
  }
}
